/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.userAgenda.service.impl;

import org.springblade.modules.hy.userAgenda.pojo.entity.UserAgendaEntity;
import org.springblade.modules.hy.userAgenda.pojo.vo.UserAgendaVO;
import org.springblade.modules.hy.userAgenda.excel.UserAgendaExcel;
import org.springblade.modules.hy.userAgenda.mapper.UserAgendaMapper;
import org.springblade.modules.hy.userAgenda.service.IUserAgendaService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 用户会议关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
public class UserAgendaServiceImpl extends BaseServiceImpl<UserAgendaMapper, UserAgendaEntity> implements IUserAgendaService {

	@Override
	public IPage<UserAgendaVO> selectUserAgendaPage(IPage<UserAgendaVO> page, UserAgendaVO userAgenda) {
		return page.setRecords(baseMapper.selectUserAgendaPage(page, userAgenda));
	}


	@Override
	public List<UserAgendaExcel> exportUserAgenda(Wrapper<UserAgendaEntity> queryWrapper) {
		List<UserAgendaExcel> userAgendaList = baseMapper.exportUserAgenda(queryWrapper);
		//userAgendaList.forEach(userAgenda -> {
		//	userAgenda.setTypeName(DictCache.getValue(DictEnum.YES_NO, UserAgenda.getType()));
		//});
		return userAgendaList;
	}

}
