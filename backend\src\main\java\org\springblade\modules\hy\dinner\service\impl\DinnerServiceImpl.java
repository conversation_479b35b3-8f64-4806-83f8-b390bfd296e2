/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.dinner.service.impl;

import org.springblade.modules.hy.dinner.pojo.entity.DinnerEntity;
import org.springblade.modules.hy.dinner.pojo.vo.DinnerVO;
import org.springblade.modules.hy.dinner.excel.DinnerExcel;
import org.springblade.modules.hy.dinner.mapper.DinnerMapper;
import org.springblade.modules.hy.dinner.service.IDinnerService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 用餐管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
public class DinnerServiceImpl extends BaseServiceImpl<DinnerMapper, DinnerEntity> implements IDinnerService {

	@Override
	public IPage<DinnerVO> selectDinnerPage(IPage<DinnerVO> page, DinnerVO dinner) {
		return page.setRecords(baseMapper.selectDinnerPage(page, dinner));
	}


	@Override
	public List<DinnerExcel> exportDinner(Wrapper<DinnerEntity> queryWrapper) {
		List<DinnerExcel> dinnerList = baseMapper.exportDinner(queryWrapper);
		//dinnerList.forEach(dinner -> {
		//	dinner.setTypeName(DictCache.getValue(DictEnum.YES_NO, Dinner.getType()));
		//});
		return dinnerList;
	}

}
