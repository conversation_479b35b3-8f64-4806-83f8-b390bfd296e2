/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.hotel.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 酒店信息表 实体类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@TableName("hy_hotel")
@Schema(description = "Hotel对象")
@EqualsAndHashCode(callSuper = true)
public class HotelEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 酒店名称
	 */
	@Schema(description = "酒店名称")
	private String hotelName;
	/**
	 * 酒店星级
	 */
	@Schema(description = "酒店星级")
	private Integer hotelRating;
	/**
	 * 酒店地址
	 */
	@Schema(description = "酒店地址")
	private String hotelAddress;
	/**
	 * 距离会场
	 */
	@Schema(description = "距离会场")
	private String distanceToVenue;
	/**
	 * 酒店电话
	 */
	@Schema(description = "酒店电话")
	private String hotelPhone;
	/**
	 * 房间类型
	 */
	@Schema(description = "房间类型")
	private String roomType;
	/**
	 * 房间设施（JSON格式）
	 */
	@Schema(description = "房间设施（JSON格式）")
	private String roomFeatures;
	/**
	 * 入住时间
	 */
	@Schema(description = "入住时间")
	private Date checkinTime;
	/**
	 * 退房时间
	 */
	@Schema(description = "退房时间")
	private Date checkoutTime;
	/**
	 * 住宿天数
	 */
	@Schema(description = "住宿天数")
	private Integer stayDays;
	/**
	 * 酒店服务（JSON格式）
	 */
	@Schema(description = "酒店服务（JSON格式）")
	private String hotelServices;
	/**
	 * 交通信息（JSON格式）
	 */
	@Schema(description = "交通信息（JSON格式）")
	private String transportInfo;
	/**
	 * 联系方式（JSON格式）
	 */
	@Schema(description = "联系方式（JSON格式）")
	private String contactInfo;
	/**
	 * 酒店提示（JSON格式）
	 */
	@Schema(description = "酒店提示（JSON格式）")
	private String hotelTips;

}
