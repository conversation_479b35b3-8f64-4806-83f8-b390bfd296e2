<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.dinner.mapper.DinnerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="dinnerResultMap" type="org.springblade.modules.hy.dinner.pojo.entity.DinnerEntity">
        <result column="id" property="id"/>
        <result column="dinner_date" property="dinnerDate"/>
        <result column="user_id" property="userId"/>
        <result column="breakfast_status" property="breakfastStatus"/>
        <result column="breakfast_qr_code" property="breakfastQrCode"/>
        <result column="lunch_status" property="lunchStatus"/>
        <result column="lunch_qr_code" property="lunchQrCode"/>
        <result column="dinner_status" property="dinnerStatus"/>
        <result column="dinner_qr_code" property="dinnerQrCode"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectDinnerPage" resultMap="dinnerResultMap">
        select * from hy_dinner where is_deleted = 0
    </select>


    <select id="exportDinner" resultType="org.springblade.modules.hy.dinner.excel.DinnerExcel">
        SELECT * FROM hy_dinner ${ew.customSqlSegment}
    </select>

</mapper>
