/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.dinner.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 用餐管理表 实体类
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@TableName("hy_dinner")
@Schema(description = "Dinner对象")
@EqualsAndHashCode(callSuper = true)
public class DinnerEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用餐日期
	 */
	@Schema(description = "用餐日期")
	private Date dinnerDate;
	/**
	 * 用户ID，关联blade_user表
	 */
	@Schema(description = "用户ID，关联blade_user表")
	private Long userId;
	/**
	 * 早餐状态（0-未用餐，1-已用餐）
	 */
	@Schema(description = "早餐状态（0-未用餐，1-已用餐）")
	private Integer breakfastStatus;
	/**
	 * 早餐二维码
	 */
	@Schema(description = "早餐二维码")
	private String breakfastQrCode;
	/**
	 * 中餐状态（0-未用餐，1-已用餐）
	 */
	@Schema(description = "中餐状态（0-未用餐，1-已用餐）")
	private Integer lunchStatus;
	/**
	 * 中餐二维码
	 */
	@Schema(description = "中餐二维码")
	private String lunchQrCode;
	/**
	 * 晚餐状态（0-未用餐，1-已用餐）
	 */
	@Schema(description = "晚餐状态（0-未用餐，1-已用餐）")
	private Integer dinnerStatus;
	/**
	 * 晚餐二维码
	 */
	@Schema(description = "晚餐二维码")
	private String dinnerQrCode;

}
